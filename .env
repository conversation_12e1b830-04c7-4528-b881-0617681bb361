# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiNzExODVmMGMtZjU3MS00OTQzLTljMmItOWZkZDQ1OGUxOGM4IiwidGVuYW50X2lkIjoiYWQzN2JlZDI5MGM4NmMwOTZkNzU0ZDQwZDY3YjkzM2M2MWE0N2U5YmMzZDZmMTI3MWZmNjE4YzRlMTQ4OTQ5MCIsImludGVybmFsX3NlY3JldCI6IjQzOGIwOWJmLWI2ZjYtNDYwNS05ZDViLTBiYTQxNzhhNmRjMyJ9.7O_lNFaQ78niaWqvcMGEPq37ziR9xPrr4qnddSHiqDg"
#DATABASE_URL="mongodb://localhost:27017/myportfolio"/

NEXT_PUBLIC_EMAIL_JS_SERVICE_ID=service_gqz5zif
NEXT_PUBLIC_EMAIL_JS_TEMPLATE_ID=template_b2repx7
NEXT_PUBLIC_EMAIL_JS_PUBLIC_KEY=z7ItqPWRkVjn_P6OG
NEXT_PUBLIC_ADMIN_API=http://localhost:8080/api/v1
NEXT_PUBLIC_SERVER_URL=http://localhost:8080/api
; NEXT_PUBLIC_ADMIN_API=http://ec2-13-233-121-63.ap-south-1.compute.amazonaws.com/api/v1
; NEXT_PUBLIC_SERVER_URL=http://ec2-13-233-121-63.ap-south-1.compute.amazonaws.com/api
GTAG_MES