{"name": "myportfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5000", "build": "next build", "start": "next start -p 5000", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.1.0", "@next/third-parties": "^15.4.4", "@prisma/client": "6.6.0", "axios": "^1.10.0", "clsx": "^2.1.0", "daisyui": "^2.51.4", "framer-motion": "^11.0.5", "highlight.js": "^11.8.0", "next": "13.2.4", "next-themes": "^0.2.1", "react": "18.2.0", "react-dom": "18.2.0", "react-stickynode": "^4.1.0", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@types/node": "24.0.3", "@types/react": "19.1.8", "autoprefixer": "^10.4.14", "eslint": "^8.37.0", "eslint-config-next": "^13.2.4", "postcss": "^8.4.21", "prisma": "6.6.0", "tailwindcss": "^3.2.7", "typescript": "5.8.3"}, "prisma": {"seed": "node ./prisma/seed.js"}}