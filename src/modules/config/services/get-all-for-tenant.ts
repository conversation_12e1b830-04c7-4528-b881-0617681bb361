import { IDataServices } from '@app/repository';
import { ConfigService } from '../config.service';
import { Logger } from '@nestjs/common';
import { extractTenantUserFromRequest } from '@app/common';
import { ConfigPresenter } from '../presenter';

export const getAllForTenant = async (
  request: any,
  db: IDataServices,
  configService: ConfigService,
) => {
  const logger = new Logger('getAllForTenant');

  try {
    // Extract tenant user from request
    // request.headers.origin = 'http://localhost:5000';
    const tenantUser = await extractTenantUserFromRequest(
      request,
      configService,
    );

    if (!tenantUser) {
      logger.warn('No tenant user found for request');
      return [];
    }

    logger.log(
      `Fetching education for tenant user: ${tenantUser.id || tenantUser}`,
    );

    // Fetch education for this specific user
    const config = await db.configs.findAll(
      {
        user: tenantUser,
        active: true,
      },
      {
        options: {
          sort: {
            startedAt: -1,
          },
        },
      },
    );

    logger.log(`Found ${config.length} config records for tenant user`);

    // return config;
    // Apply presenter and return only active education records
    return config
      .filter((cfg) => cfg.active)
      .map((cfg) => new ConfigPresenter(cfg));
  } catch (error) {
    logger.error('Error in getAllForTenant:', error);
    return [];
  }
};
