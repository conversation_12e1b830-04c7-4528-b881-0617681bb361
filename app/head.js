/*
 * File           : Head.js
 * Project        : myportfolio
 * Created Date   : We 15 Mar 2023 08:25:13
 * Description    : <<description>>
 *
 *
 * Author         : <PERSON><PERSON><PERSON>
 * Email          : <EMAIL>
 * ----------------------
 * Last Modified  : Wed Mar 15 2023
 * Modified By    : <PERSON><PERSON><PERSON>
 * ------------------------
 */

const Head = () => {
  return (
    <>
      <title>Tanzim's Portfolio</title>
      {/*<Script id="gtm-script" strategy="afterInteractive">*/}
      {/*  {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':*/}
      {/*      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],*/}
      {/*      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=*/}
      {/*      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);*/}
      {/*  })(window,document,'script','dataLayer','GTM-WRBB644G');`}*/}
      {/*</Script>*/}
      {/*<Script*/}
      {/*  id="gtag-js"*/}
      {/*  src="https://www.googletagmanager.com/gtag/js?id=G-YDMPYGK4BQ"*/}
      {/*  strategy="afterInteractive"*/}
      {/*/>*/}
      {/*<Script*/}
      {/*  id="gtag-init"*/}
      {/*  strategy="afterInteractive"*/}
      {/*  dangerouslySetInnerHTML={{*/}
      {/*    __html: `*/}
      {/*      window.dataLayer = window.dataLayer || [];*/}
      {/*      function gtag(){dataLayer.push(arguments);}*/}
      {/*      gtag('js', new Date());*/}
      {/*      gtag('config', 'G-YDMPYGK4BQ');*/}
      {/*    `*/}
      {/*  }}*/}
      {/*/>*/}
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="description" content="Tanzim Ahmed's Portfolio" />
      <meta name="author" content="Tanzim Ahmed" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      <link
        href="https://fonts.googleapis.com/css2?family=Fira+Code&display=swap"
        rel="stylesheet"
      />
    </>
  );
};

export default Head;
