import axios from 'axios'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/v1/social-media/data`

export async function GET(request) {
    try {

        const origin = new URL(request.url).origin;

        const res = await axios.get(baseUrl, {
            headers :{
                referer : origin
            }
        })
        const data = res.data
        return new Response(JSON.stringify(data), {
            status: res.status,
            headers: {
                'Content-Type': 'application/json'
            }
        })
    } catch (error) {
        console.log(error)
    }
}

